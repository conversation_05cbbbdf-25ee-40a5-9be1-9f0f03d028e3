spring:
  datasource:
    mysql:
      url: ************************************************************************************
      username: dev
      password: devdev
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 2 # 使用最小的连接池，2是因为relatedColumn+Stream需要2个连接池同时来
    clickhouse:
      jdbc-url: **********************************************
      username: root
      password: 123456
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    postgresql:
      jdbc-url: ********************************************************************************************
      username: postgres
      password: homeserver
      driver-class-name: org.postgresql.Driver

# 是否运行压测测试
runBenchmarkTest: false
# 是否运行异常测试
runExceptionTest: false