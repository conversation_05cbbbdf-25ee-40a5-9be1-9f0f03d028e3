<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>

		<!-- 最低支持的版本是2.2.0.RELEASE 最高不限；所以这里测试最低和最高都需要各测一次-->
		<version>2.4.0</version>

		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.pugwoo</groupId>
	<artifactId>nimble-orm-test</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>nimble-orm-test</name>
	<description>nimble-orm-test</description>
	<properties>
		<java.version>1.8</java.version>
	</properties>
	<dependencies>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.clickhouse</groupId>
			<artifactId>clickhouse-jdbc</artifactId>
			<version>0.7.2</version>
		</dependency>
		<!-- clickhosue-jdbc 0.4.6+ 默认使用lz4压缩，所以需要引入lz4-java包 -->
		<dependency>
			<groupId>org.lz4</groupId>
			<artifactId>lz4-java</artifactId>
			<version>1.8.0</version>
		</dependency>
		<!-- clickhouse优先用这个 -->
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
			<version>5.3.1</version>
		</dependency>

		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.7.3</version>
		</dependency>

		<dependency>
			<groupId>com.pugwoo</groupId>
			<artifactId>nimble-orm</artifactId>
			<version>1.7.5</version>
		</dependency>

		<dependency>
			<groupId>com.pugwoo</groupId>
			<artifactId>woo-utils</artifactId>
			<version>1.3.5</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.32</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
