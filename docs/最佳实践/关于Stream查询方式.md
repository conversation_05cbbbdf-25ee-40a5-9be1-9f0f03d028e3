## 背景

假设现在有一张大表，数据量有100万以上甚至1000万行，我们需要遍历这张表，获取到这张表的每一行并进行一些处理。

最简单的做法，是`select * from table`将整张表都load到内存中，然后对数据进行处理。

这种做法有两个缺点：

1) 内存占用过大，按经验，一条几十个字段的记录，在Java内存中大概占1KB~2KB，那么100万行的数据将占用1GB~2GB，再大的数据量将占用10GB级别的内存，很容易导致OOM。

2) 数据执行该SQL的耗时会比较长，它会遍历整张表将数据取出，这个时长对于大表可能长达10分钟至1小时；取出数据之后再通过网络传输给Java客户端，客户端接收到数据之后再反序列化成Java对象。SQL执行和传输是串行的，这意味着对于大表，Java客户端至少要等待10分钟到1小时，才能开始处理数据。

## 现有做法如何解决？

解决该问题并不难，现有一般这么做：

1) 按id排序，先查出前10000条，`select * from table order by id limit 10000`，load入内存处理，记住这10000条中最大的id。

2) 处理完之后，再从这个最大的id开始，再取下一个10000条进行处理：`select * from table where id>上一批最大id order by id limit 10000`

3) 如此进行，直到查不出数据为止。

这种处理方式有效地解决了上面的2个缺点问题。

## 有没有更好的做法？

首先说明一下为何期望有更好的做法，因为现有的做法还比较手工，当表有多个主键或无主键时，分页处理较易出错。

期望可以结合Java的Stream流这样来做：

```java
Stream<StudentDO> all = jdbcTemplate.getForStream("select * from table");
// 以Stream方式处理数据
```

这种方式，同样解决了内存问题，还不需要手工去分页，不用关心主键有几个。

可惜的是，这种方式目前在MySQL上并未很好地支持，效果是背景缺点2，仍不支持。实际上，MySQL还是要执行完这条SQL才传输回来，这个执行的时间客户端需要等待；在实现上，是jdbc通过阻塞网络来控制接收的数据，从而避免了内存占用过大的问题。

所以，这种方式，内存问题是解决了的，不用担心；但是性能不佳，对于大表，客户端要等待较长时间，不像手工分页那样可以很快返回；而且，这种方式会占用mysql的一条连接，导致线程池连接复用能力急剧下降，所以对于大表非常不建议使用。

该问题目前仍无解决方式，无法解决的原因是受限于MySQL自身不支持。期待未来的MySQL版本可以支持。